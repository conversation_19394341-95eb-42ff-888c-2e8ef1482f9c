#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "ゲームを終了して、オンラインで解決策を探す";
	btn_exit = "ゲームを終了する";
	btn_hidedetails = "詳細を閉じる";
	btn_showdetails = "詳細を表示";
	chk_sendreport = "エラーレポートを送信する";
	error_code = "エラーコード:";
	lbl_body1 = "申し訳ありません、ゲーム起動時に問題が発生しました";
	lbl_body2 = "お手数ですが、この問題の報告をお願いいたします。";
	lbl_body3 = "Easy Anti-Cheatはオンラインで解決策を検索することができます。問題解決の助けになれば幸いです。";
	lbl_header = "ゲームを起動できません";
	title = "起動時エラー";
};
game_error:
{
	error_catalogue_corrupted = "Easy Anti-Cheatのカタログファイルのハッシュが破損しています";
	error_catalogue_not_found = "EAC インデックスが見つかりません";
	error_certificate_revoked = "EAC インデックス認証が無効となりました";
	error_corrupted_memory = "メモリが破損しています";
	error_corrupted_network = "パケットフローが破損しています";
	error_file_forbidden = "不明なゲームファイルです";
	error_file_not_found = "必要なファイルが見つかりません";
	error_file_version = "不明なファイルバージョンです";
	error_module_forbidden = "禁止されているモジュールです";
	error_system_configuration = "禁止されているシステム構成です";
	error_system_version = "信頼性の低いシステムファイルです";
	error_tool_forbidden = "禁止されているツールです";
	error_violation = "チート防止内部エラー";
	error_virtual = "仮想マシン上では稼働できません";
	peer_client_banned = "Anti-cheatピアが禁止されています。";
	peer_heartbeat_rejected = "Anti-cheatピアが拒否されました。";
	peer_validated = "Anti-cheatピア認証が完了しました。";
	peer_validation_failed = "Anti-cheatピア認証ができませんでした。";
	executable_not_hashed = "カタログにゲームの実行ファイルのエントリが見つかりません。";
};
launcher:
{
	btn_cancel = "キャンセル";
	btn_exit = "終了";
	error_cancel = "起動がキャンセルされました";
	error_filenotfound = "ファイルが見つかりません";
	error_init = "初期化エラー";
	error_install = "インストールエラー";
	error_launch = "起動時エラー";
	error_nolib = "Easy Anti-Cheatライブラリをロードできません";
	loading = "ロード中";
	wait = "お待ちください";
	initializing = "初期化中";
	success_waiting_for_game = "ゲームを待っています";
	success_closing = "成功";
	network_error = "ネットワークエラー";
	error_no_settings_file = "{0} が見つかりませんでした";
	error_invalid_settings_format = "{0} は有効な JSON 形式ではありません";
	error_missing_required_field = "{0} には必須フィールド ({1}) が不足しています";
	error_invalid_eos_identifier = "{0}に無効なEpic Online Services（EOS）識別子：（{1}）が含まれています";
	download_progress = "ダウンロードの進行状況： {0}";
};
launcher_error:
{
	error_already_running = "Easy Anti-Cheatを使用したアプリケーションがすでに稼働しています！ {0}";
	error_application = "ゲームクライアントがアプリケーションエラーを起こしました. エラーコード: {0}";
	error_bad_exe_format = "64ビットOSが必須です";
	error_bitset_32 = "ゲームの32-bitバージョンをご使用ください";
	error_bitset_64 = "ゲームの64-bitバージョンをご使用ください";
	error_cancelled = "実行がユーザーによってキャンセルされました";
	error_certificate_validation = "Easy Anti-Cheatコード署名証明書の認証エラー";
	error_connection = "コンテンツデリバリネットワークへ接続できませんでした！";
	error_debugger = "デバッガーが検知されました。デバッガーをアンロードしてからやり直してください";
	error_disk_space = "ディスクの空き容量不足です。";
	error_dns = "コンテンツデリバリネットワークへのDNSリゾルブができませんでした！";
	error_dotlocal = "DotLocal DLLのリダイレクトが検知されました";
	error_dotlocal_instructions = "以下のファイルを削除してください";
	error_file_not_found = "ファイルが見つかりません:";
	error_forbidden_tool = "{0}を閉じてから、ゲームを開始してください";
	error_forbidden_driver = "ゲームを開始する前に{0}をアンロードしてください";
	error_generic = "不明なエラー";
	error_kernel_debug = "カーネルデバックが有効になっていると、Easy Anti-Cheatは稼働できません";
	error_kernel_dse = "Driver Signature Enforcementが無効になっていると、Easy Anti-Cheatは稼働できません";
	error_kernel_modified = "禁止されているWindowsカーネル変更が検知されました";
	error_library_load = "Easy Anti-Cheatライブラリをロードできません";
	error_memory = "メモリ不足でゲームを起動できません";
	error_module_load = "チート防止モジュールをロードできませんでした";
	error_patched = "パッチされたWindows ブートローダーが検知されました";
	error_process = "プロセスを作成できません";
	error_process_crash = "処理が突然終了しました";
	error_safe_mode = "Easy Anti-CheatはWindowsセーフモードでは実行できません";
	error_socket = "アプリケーションのインターネットへのアクセスを何かが妨げています！";
	error_ssl = "CDNサービスとのSSL接続確立時にエラー！";
	error_start = "ゲームを開始できませんでした";
	error_uncpath_forbidden = "ネットワークシェアを通してゲームを実行できません。（UNCパス）";
	error_connection_failed = "接続に失敗しました: ";
	error_missing_game_id = "ゲームIDが見つかりません";
	error_dns_resolve_failed = "プロキシへのDNS解決に失敗しました";
	error_dns_connection_failed = "コンテンツ配信ネットワークへの接続に失敗しました！Curl コード： {0}!";
	error_http_response = "HTTPレスポンスコード： {0} Curl コード： {1}";
	error_driver_handle = "予期せぬエラー（ドライバーハンドルを開けませんでした）";
	error_incompatible_service = "互換性のないEasy Anti-Cheatサービスがすでに実行されています。実行中の他のゲームを終了するか再起動してください";
	error_incompatible_driver_version = "互換性のないEasy Anti-Cheatドライバのバージョンがすでに実行されています。実行中の他のゲームを終了するか再起動してください";
	error_another_launcher = "予期せぬエラー（他のランチャーが実行中です）";
	error_game_running = "予期せぬエラー（ゲームはすでに実行されています）";
	error_patched_boot_loader = "パッチが適用されたWindwowsブートローダが検出されました。（Kernel Patch Protectionが無効です）";
	error_unknown_process = "認識できないゲームクライアントです。継続できません";
	error_unknown_game = "未設定のゲームです。継続できません。";
	error_win7_required = "Windows 7以降が必要です";
	success_initialized = "Easy Anti-Cheatが初期化されました";
	success_loaded = "Easy Anti-Cheatのゲームへのロードが完了しました";
	error_create_process = "ゲームプロセス：{0}の作成に失敗しました";
	error_create_thread = "バックグラウンドスレッドの作成に失敗しました！";
	error_disallowed_cdn_path = "予期しないエラー。（CDNのURLに誤りがあります）";
	error_empty_executable_field = "ゲームバイナリへのパスが提供されていません。";
	error_failed_path_query = "プロセスのパスの取得に失敗しました";
	error_failed_to_execute = "ゲームプロセスの実行に失敗しました。";
	error_game_binary_is_directory = "ターゲットの実行可能ファイルがディレクトリです！";
	error_game_binary_is_dot_app = "ターゲットの実行可能ファイルがディレクトリです。代わりに.app内のバイナリをターゲットにしてください。";
	error_game_binary_not_found = "ゲームバイナリ：「{0}」が見つかりませんでした";
	error_game_binary_not_found_wine = "ゲームバイナリの検索に失敗しました（Wine）";
	error_game_security_violation = "ゲームセキュリティ違反：{0}";
	error_generic_ex = "予期しないエラー。{0}";
	error_instance_count_limit = "ゲームの最大同時インスタンスに達しました！";
	error_internal = "内部エラー！";
	error_invalid_executable_path = "ゲームの実行パスが無効です！";
	error_memory_ex = "ゲーム（{0}）を開始するためのメモリが不足しています";
	error_missing_binary_path = "ゲームの実行ファイルのパスがありません。";
	error_missing_directory_path = "作業ディレクトリパスがありません。";
	error_module_initialize = "{0}でモジュールの初期化に失敗しました";
	error_module_loading = "Anti-Cheatモジュールの読み込みに失敗しました。";
	error_set_environment_variables = "ゲームプロセスの環境変数の設定に失敗しました。";
	error_unrecognized_blacklisted_driver = "N/Aが検出されました。それをアンロードしてから、再試行してください。";
	error_unsupported_machine_arch = "サポートされていないホストマシンのアーキテクチャ。（{0}）";
	error_working_directory_not_found = "作業ディレクトリが存在しません。";
	error_x8664_required = "サポートされていないOSです。64ビット（x86-64）バージョンのWindowsが必要です。";
	warn_module_download_size = "HTTPレスポンスサイズ：{0}。Null（ヌル）クライアントモードで開始します。";
	warn_vista_deprecation = "Easy Anti-Cheatは、互換性のあるコード署名を作成できなくなったため、Windows Vistaのサポートを2020年10月をもって終了します。https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsusをご参照ください。";
	error_configuration = "アンチチートの設定を検証できませんでした。";
	warn_win7_update_required = "Windows Updateを実行してください。ご利用のシステムには、2020年10月までに必要とされた重要なSHA-2コード署名のサポートがありません。https://support.microsoft.com/help/4474419/sha-2-code-signing-support-updateをご参照ください。";
	error_service_update_timeout = "Easy Anti-Cheatサービスの更新がタイムアウトしました。"
	error_launch_ex = "起動時エラー: {0}";
};
setup:
{
	btn_finish = "終了";
	btn_install = "今すぐインストール";
	btn_repair = "修復サービス";
	btn_uninstall = "アンインストール";
	epic_link = "© Epic Games, Inc";
	install_progress = "インストール中…";
	install_success = "インストールが完了しました";
	licenses_link = "ライセンス";
	privacy_link = "プライバシー";
	repair_progress = "修復中…";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "アンインストール中…";
	uninstall_success = "アンインストールが完了しました";
};
setup_error:
{
	error_cancelled = "実行がユーザーによってキャンセルされました";
	error_encrypted = "Easy Anti-Cheatインストレーションフォルダが暗号化されています";
	error_intro = "Easy Anti-Cheatのセットアップができませんでした";
	error_not_installed = "Easy Anti-Cheatがインストールされていません";
	error_registry = "実行ファイルのコピーに失敗（32）";
	error_rights = "権限が不足しています";
	error_service = "サービスを作成できません";
	error_service_ex = "サービスを作成できません {0}";
	error_system = "System32へのアクセスが拒否されました";
};