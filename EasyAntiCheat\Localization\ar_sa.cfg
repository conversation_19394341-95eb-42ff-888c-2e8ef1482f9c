#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "اتصل بالإنترنت للبحث عن حل وأغلق اللعبة.";
	btn_exit = "أغلق اللعبة.";
	btn_hidedetails = "أخفي التفاصيل";
	btn_showdetails = "أظهر التفاصيل";
	chk_sendreport = "ارسال رمز الخطأ";
	error_code = "رمز الخطأ:";
	lbl_body1 = "نعتذر، لدينا مشكلة في بدء لعبتك";
	lbl_body2 = "يُرجى مساعدتنا بالإبلاغ عن تلك المشكلة.";
	lbl_body3 = "يمكن أن تقوم Easy Anti-Cheat بالبحث عن حل للمشكلة على الإنترنت والمحاولة في المساعدة لحلها.";
	lbl_header = "لا يمكن تشغيل اللعبة";
	title = "خطأ تشغيل";
};
game_error:
{
	error_catalogue_corrupted = "كتالوج تجزئة Easy Anti-Cheat تالف";
	error_catalogue_not_found = "فهرس EAC غير موجود";
	error_certificate_revoked = "تم إلغاء شهادة فهرس EAC ";
	error_corrupted_memory = "ذاكرة تالفة";
	error_corrupted_network = "تدفق تالف للحزمة";
	error_file_forbidden = "ملف لعبة غير معروف";
	error_file_not_found = "ملف مطلوب مفقود";
	error_file_version = "إصدار ملف غير معروف";
	error_module_forbidden = "نموذج ممنوع";
	error_system_configuration = "تهيئة نظام ممنوعة";
	error_system_version = "ملف نظام غير موثوق";
	error_tool_forbidden = "أداة ممنوعة";
	error_violation = "خطأ مكافحة غش داخلي";
	error_virtual = "لا يمكن التشغيل على جهاز افتراضي";
	peer_client_banned = "تم منع كشف غش النظير.";
	peer_heartbeat_rejected = "تم رفض كشف غش النظير";
	peer_validated = "تم اكتمال التحقق من كشف غش النظير.";
	peer_validation_failed = "فشل التحقق من كشف غش النظير.";
	executable_not_hashed = "تعذر تحديد موقع إدخال الملف القابل للتنفيذ الخاص باللعبة في الفهرس.";
};
launcher:
{
	btn_cancel = "إلغاء";
	btn_exit = "خروج";
	error_cancel = "تم إلغاء التشغيل";
	error_filenotfound = "لم يتم العثور على الملف";
	error_init = "مشكلة في البدء";
	error_install = "خطأ تثبيت";
	error_launch = "خطأ تشغيل";
	error_nolib = "لا يمكن تحميل مكتبة Easy Anti-Cheat";
	loading = "جارِ التحميل";
	wait = "يُرجى الإنتظار";
	initializing = "جارِ التهيئة";
	success_waiting_for_game = "في انتظار تحميل اللعبة";
	success_closing = "تم بنجاح";
	network_error = "خطأ بالشبكة";
	error_no_settings_file = "لم يتم العثور على {0}";
	error_invalid_settings_format = "ليس لدى {0} تنسيق JSON صالح";
	error_missing_required_field = "{0} يفقد الحقل المطلوب ({1})";
	error_invalid_eos_identifier = "{0} يتضمن معرف EOS غير صالح: ({1})";
	download_progress = "تقدم التنزيل: {0}";
};
launcher_error:
{
	error_already_running = "هناك تطبيق يستخدم Easy Anti-Cheat قيد التشغيل بالفعل! {0}";
	error_application = "اكتشف عميل اللعبة خطأ بالتطبيق. رمز الخطأ: {0}";
	error_bad_exe_format = "يلزم وجود نظام تشغيل من الإصدار 64 بت";
	error_bitset_32 = "برجاء استخدام اصدار 32 بت من اللعبة";
	error_bitset_64 = "برجاء استخدام اصدار 64 بت من اللعبة";
	error_cancelled = "تم إلغاء العملية من قبل العميل";
	error_certificate_validation = "خطأ توثيق شهادة توقيع تعليمات برمجية لـ Easy Anti-Cheat";
	error_connection = "فشل الاتصال بشبكة توزيع المحتوى!";
	error_debugger = "تم اكتشاف مصحح أخطاء. برجاء إلغاء تحميله وإعادة المحاولة";
	error_disk_space = "لا يوجد مساحة كافية على القرص.";
	error_dns = "فشل حل DNS لشبكة توزيع المحتوى!";
	error_dotlocal = "تم التعرف على إعادة توجيه DotLocal DLL.";
	error_dotlocal_instructions = "يرجى حذف الملف التالي";
	error_file_not_found = "لم يتم العثور على الملف:";
	error_forbidden_tool = "برجاء إغلاق {0} قبل بدء اللعبة";
	error_forbidden_driver = "يُرجى تحميل {0} قبل بدء تشغيل اللعبة";
	error_generic = "خطأ غير متوقع.";
	error_kernel_debug = "لا يمكن تشغيل Easy Anti-Cheat إذا كان Kernel Debugging ممكنًا";
	error_kernel_dse = "لا يمكن تشغيل Easy Anti-Cheat إذا كان Driver Signature Enforcement معطلاً";
	error_kernel_modified = "تم التعرف على تعديل Windows kernel ممنوع";
	error_library_load = "لا يمكن تحميل مكتبة Easy Anti-Cheat";
	error_memory = "ذاكرة غير كافية لبدء اللعبة";
	error_module_load = "فشل تحميل وحدة مكافح الغش";
	error_patched = "تم التعرف على محمل إقلاع Windows معدل";
	error_process = "لا يمكن إنشاء العملية";
	error_process_crash = "تم إنهاء العملية بصورة مفاجئة";
	error_safe_mode = "لا يمكن أن تعمل Easy Anti-Cheat في الوضع الآمن لـ Windows";
	error_socket = "هناك شيء يمنع التطبيق من الوصول للإنترنت!";
	error_ssl = "خطأ إنشاء اتصال SSL مع خدمة CDN!";
	error_start = "فشل بدء اللعبة";
	error_uncpath_forbidden = "لا يمكن تشغيل اللعبة من خلال مشاركة شبكة. (مسار (UNC)";
	error_connection_failed = "فشل الاتصال: ";
	error_missing_game_id = "مُعرف اللعبة المفقودة";
	error_dns_resolve_failed = "فشل حل نظام أسماء المجالات (DNS) إلى الوكيل";
	error_dns_connection_failed = "فشل الاتصال بشبكة توزيع المحتوى! رمز Curl: {0}!";
	error_http_response = "رمز استجابة HTTP: {0} رمز Curl: {1}";
	error_driver_handle = "خطأ غير متوقع. (فشل فتح مؤشر برنامج التشغيل)";
	error_incompatible_service = "هناك خدمة Easy Anti-Cheat غير متوافقة قيد التشغيل بالفعل. يُرجى الخروج من الألعاب الأخرى قيد التشغيل أو قم بإعادة التشغيل";
	error_incompatible_driver_version = "هناك إصدار برنامج تشغيل Easy Anti-Cheat غير متوافق قيد التشغيل بالفعل. يُرجى الخروج من الألعاب الأخرى قيد التشغيل أو قم بإعادة التشغيل";
	error_another_launcher = "خطأ غير متوقع. (مُشغل آخر قيد التشغيل بالفعل)";
	error_game_running = "خطأ غير متوقع. (اللعبة قيد التشغيل بالفعل)";
	error_patched_boot_loader = "تم الكشف عن أداة تحميل تمهيد Windows المصححة. (تم تعطيل حماية تصحيح Kernel)";
	error_unknown_process = "عميل لعبة غير معروف. لا يمكن الاستمرار.";
	error_unknown_game = "لعبة غير مكوّنة. تتعذر المتابعة.";
	error_win7_required = "يتطلب وجود اصدار Windows 7 أو إصدار لاحق.";
	success_initialized = "تم بدء Easy Anti-Cheat بنجاح";
	success_loaded = "تم تحميل Easy Anti-Cheat بنجاح في اللعبة";
	error_create_process = "فشل إنشاء عملية اللعبة: {0}";
	error_create_thread = "فشل إنشاء سلسلة الخلفية!";
	error_disallowed_cdn_path = "خطأ غير متوقع. (عنوان URL غير صحيح لـ CDN)";
	error_empty_executable_field = "لم يتم توفير المسار إلى الملف الثنائي للعبة.";
	error_failed_path_query = "فشل الوصول إلى مسار العملية";
	error_failed_to_execute = "فشل تنفيذ عملية اللعبة.";
	error_game_binary_is_directory = "يُعتبر الملف القابل للتنفيذ الهدف دليلاً!";
	error_game_binary_is_dot_app = "يُعتبر الملف القابل للتنفيذ الهدف دليلاً، استهدف الملف الثنائي داخل .app بدلاً من ذلك!";
	error_game_binary_not_found = "فشل تحديد مكان الملف الثنائي للعبة: '{0}'";
	error_game_binary_not_found_wine = "فشل تحديد مكان الملف الثنائي للعبة (Wine)";
	error_game_security_violation = "اختراق أمني في اللعبة {0}";
	error_generic_ex = "خطأ غير متوقع. {0}";
	error_instance_count_limit = "تم الوصول إلى الحد الأقصى من حالات اللعبة المتزامنة!";
	error_internal = "خطأ داخلي!";
	error_invalid_executable_path = "مسار الملف التنفيذي للعبة غير صالح!";
	error_memory_ex = "ذاكرة غير كافية لبدء اللعبة {0}";
	error_missing_binary_path = "المسار التنفيذي للعبة مفقود.";
	error_missing_directory_path = "مسار دليل العمل مفقود.";
	error_module_initialize = "فشل تمهيد الوحدة مع {0}";
	error_module_loading = "فشل تحميل وحدة مكافحة الغش.";
	error_set_environment_variables = "فشل ضبط متغيرات البيئة لعملية اللعبة.";
	error_unrecognized_blacklisted_driver = "N/A تم اكتشافها. يرجى إلغاء تحميلها ثم إعادة المحاولة.";
	error_unsupported_machine_arch = "بنية الجهاز المضيف غير مدعومة. ({0})";
	error_working_directory_not_found = "دليل العمل غير موجود.";
	error_x8664_required = "نظام تشغيل غير مدعوم. مطلوب إصدار 64 بت (x86-64) من Windows.";
	warn_module_download_size = "حجم استجابة HTTP: {0}. جارٍ البدء في وضع عميل فارغ.";
	warn_vista_deprecation = "يجب أن تنهي Easy Anti-Cheat دعمها لنظام التشغيل Windows Vista في أكتوبر 2020 لأنه لم يعد من الممكن إنشاء توقيعات رموز متوافقة. راجع https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "تعذر التحقق من صحة تهيئة مكافحة الغش.";
	warn_win7_update_required = "يرجى تشغيل تحديثات Windows، حيث يفتقر نظامك إلى دعم توقيع رمز SHA-2 الأساسي المطلوب بحلول أكتوبر 2020. راجع https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "انقضت مهلة تحديث خدمة Easy Anti-Cheat."
	error_launch_ex = "خطأ تشغيل: {0}";
};
setup:
{
	btn_finish = "إنهاء";
	btn_install = "قم بالتثبيت الآن";
	btn_repair = "إصلاح الخدمة";
	btn_uninstall = "إلغاء تثبيت";
	epic_link = "© Epic Games, Inc";
	install_progress = "جارِ التثبيت...";
	install_success = "تم التثبيت بنجاح";
	licenses_link = "الترخيصات";
	privacy_link = "الخصوصية";
	repair_progress = "جارِ الإصلاح";
	title = "تنصيب خدمة Easy Anti-Cheat";
	uninstall_progress = "ِ إلغاء التثبيت";
	uninstall_success = "تم إلغاء التثبيت بنجاح";
};
setup_error:
{
	error_cancelled = "تم إلغاء العملية من قبل العميل";
	error_encrypted = "Easy Anti-Cheat تم تشفير مجلد تثبيت";
	error_intro = "فشل تنصيب Easy Anti-Cheat";
	error_not_installed = "لم يتم تثبيت Easy Anti-Cheat";
	error_registry = "الوصول للسجل ممنوع";
	error_rights = "إمتيازات غير كافية";
	error_service = "لا يمكن إلغاء الخدمة";
	error_service_ex = "لا يمكن إلغاء الخدمة {0}";
	error_system = "الوصول لـ System32 ممنوع";
};