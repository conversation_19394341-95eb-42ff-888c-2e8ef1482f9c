#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "Chercher une solution en ligne et quitter le jeu.";
	btn_exit = "Quitter le jeu.";
	btn_hidedetails = "Masquer les détails";
	btn_showdetails = "Afficher les détails";
	chk_sendreport = "Envoyer le rapport d'erreur";
	error_code = "Code d'erreur :";
	lbl_body1 = "Nous sommes désolés, un problème est survenu lors du démarrage de votre partie";
	lbl_body2 = "Veuillez nous aider en signalant ce problème.";
	lbl_body3 = "Easy Anti-Cheat peut chercher une solution au problème en ligne et tenter de vous aider à le résoudre.";
	lbl_header = "Impossible de démarrer la partie";
	title = "Erreur de lancement";
};
game_error:
{
	error_catalogue_corrupted = "Catalogue de hachage d'Easy Anti-Cheat corrompu";
	error_catalogue_not_found = "Index EAC introuvable";
	error_certificate_revoked = "Certificat de l'index EAC révoqué";
	error_corrupted_memory = "Mémoire corrompue";
	error_corrupted_network = "Flux de paquets corrompu";
	error_file_forbidden = "Fichier de jeu inconnu";
	error_file_not_found = "Fichier requis manquant";
	error_file_version = "Version de fichier inconnue";
	error_module_forbidden = "Module non-autorisé";
	error_system_configuration = "Configuration de système non-autorisée";
	error_system_version = "Fichier système non-fiable";
	error_tool_forbidden = "Outil non-autorisé";
	error_violation = "Erreur anti-triche interne";
	error_virtual = "Exécution impossible sur une machine virtuelle.";
	peer_client_banned = "Joueur bloqué par le processus anti-triche.";
	peer_heartbeat_rejected = "Joueur rejeté par le processus anti-triche.";
	peer_validated = "Validation anti-triche du joueur terminée.";
	peer_validation_failed = "Validation anti-triche du joueur échouée.";
	executable_not_hashed = "Impossible de localiser l'entrée exécutable du jeu dans le catalogue.";
};
launcher:
{
	btn_cancel = "Annuler";
	btn_exit = "Quitter";
	error_cancel = "Lancement annulé";
	error_filenotfound = "Fichier introuvable";
	error_init = "Erreur d'initialisation";
	error_install = "Erreur d'installation";
	error_launch = "Erreur de lancement";
	error_nolib = "Chargement de la bibliothèque Easy Anti-Cheat impossible";
	loading = "CHARGEMENT";
	wait = "Veuillez patienter";
	initializing = "INITIALISATION";
	success_waiting_for_game = "EN ATTENTE DU JEU";
	success_closing = "Opération réussie";
	network_error = "Erreur réseau";
	error_no_settings_file = "{0} introuvable";
	error_invalid_settings_format = "{0} n'a pas de format JSON valide";
	error_missing_required_field = "Champ obligatoire manquant pour {0} ({1})";
	error_invalid_eos_identifier = "{0} contient un identifiant EOS non valide : ({1})";
	download_progress = "Progression du téléchargement : {0}";
};
launcher_error:
{
	error_already_running = "Une application utilisant Easy Anti-Cheat est déjà en cours d'exécution ! {0}";
	error_application = "Une erreur d'application s'est produite dans le client du jeu. Code d'erreur: {0}";
	error_bad_exe_format = "Système d'exploitation 64 bits requis";
	error_bitset_32 = "Veuillez utiliser la version 32-bit du jeu";
	error_bitset_64 = "Veuillez utiliser la version 64-bit du jeu";
	error_cancelled = "Opération annulée par l'utilisateur";
	error_certificate_validation = "Erreur lors de la validation du certificat de signature de code Easy Anti-Cheat";
	error_connection = "Échec de la connexion au réseau de distribution de contenu !";
	error_debugger = "Un débogueur a été détecté. Veuillez le désinstaller et réessayer.";
	error_disk_space = "Espace disque insuffisant.";
	error_dns = "Échec de résolution de DNS sur le réseau de distribution de contenu !";
	error_dotlocal = "Redirection DotLocal DLL détectée.";
	error_dotlocal_instructions = "Veuillez supprimer le fichier suivant";
	error_file_not_found = "Fichier introuvable :";
	error_forbidden_tool = "Veuillez fermer {0} avant de démarrer le jeu";
	error_forbidden_driver = "Veuillez décharger {0} avant de démarrer le jeu";
	error_generic = "Erreur inattendue.";
	error_kernel_debug = "Easy Anti-Cheat ne peut pas s'exécuter si Kernel Debugging est activé";
	error_kernel_dse = "Easy Anti-Cheat ne peut pas s'exécuter si Driver Signature Enforcement a été désactivé.";
	error_kernel_modified = "Modification Windows Kernel non-autorisée détectée";
	error_library_load = "Chargement de la bibliothèque Easy Anti-Cheat impossible";
	error_memory = "Mémoire insuffisante pour démarrer le jeu";
	error_module_load = "Échec du chargement du module anti-triche";
	error_patched = "Chargeur de démarrage patché détecté";
	error_process = "Création du processus impossible";
	error_process_crash = "Le processus s'est terminé abruptement";
	error_safe_mode = "Easy Anti-Cheat ne peut pas s'exécuter dans le mode sans échec de Windows";
	error_socket = "Quelque chose empêche l'application d'accéder à Internet !";
	error_ssl = "Erreur d'établissement de connexion SSL avec le service CDN !";
	error_start = "Échec du démarrage du jeu";
	error_uncpath_forbidden = "Impossible d'exécuter le jeu via un partage réseau. (chemin d'accès UNC)";
	error_connection_failed = "Échec de la connexion: ";
	error_missing_game_id = "L'identifiant du jeu est manquant";
	error_dns_resolve_failed = "La résolution DNS au proxy a échoué";
	error_dns_connection_failed = "La connexion au réseau de distribution du contenu a échoué ! Code cURL : {0} !";
	error_http_response = "Code de réponse HTTP : {0} Code cURL : {1}";
	error_driver_handle = "Erreur inattendue. (L'ouverture de l'identificateur du pilote a échoué)";
	error_incompatible_service = "Une version incompatible du service Easy Anti-Cheat est en cours d'exécution. Quittez les autres jeux en cours ou redémarrez.";
	error_incompatible_driver_version = "Une version incompatible du pilote Easy Anti-Cheat est en cours d'exécution. Quittez les autres jeux en cours ou redémarrez";
	error_another_launcher = "Erreur inattendue. (Un autre lanceur est déjà en cours d’exécution)";
	error_game_running = "Erreur inattendue. (Un jeu est déjà en cours d’exécution)";
	error_patched_boot_loader = "Chargeur de démarrage Windows corrigé détecté. (La fonctionnalité Kernel Patch Protection est désactivée)";
	error_unknown_process = "Client de jeu non reconnu. Impossible de continuer.";
	error_unknown_game = "Jeu non configuré. Impossible de continuer.";
	error_win7_required = "Windows 7 ou SE plus récent requis.";
	success_initialized = "Easy Anti-Cheat initialisé avec succès";
	success_loaded = "Easy Anti-Cheat chargé dans le jeu avec succès";
	error_launch_ex = "Erreur de lancement: {0}";
};
setup:
{
	btn_finish = "Terminer";
	btn_install = "Installer maintenant";
	btn_repair = "Service de réparation";
	btn_uninstall = "Désinstaller";
	epic_link = "© Epic Games, Inc";
	install_progress = "Installation en cours...";
	install_success = "Installation réussie";
	licenses_link = "Licences";
	privacy_link = "Confidentialité";
	repair_progress = "Réparation en cours...";
	title = "Configuration du service Easy Anti-Cheat";
	uninstall_progress = "Désinstallation en cours...";
	uninstall_success = "Désinstallation réussie";
	error_create_process = "Échec de la création du processus du jeu : {0}";
	error_create_thread = "Échec de la création du fil d'arrière-plan !";
	error_disallowed_cdn_path = "Erreur inattendue. (URL RDC incorrecte)";
	error_empty_executable_field = "Le chemin d'accès du fichier binaire du jeu n'a pas été fourni.";
	error_failed_path_query = "Échec de récupération du chemin d'accès du processus";
	error_failed_to_execute = "Échec d'exécution du processus du jeu.";
	error_game_binary_is_directory = "L'exécutable cible est un répertoire !";
	error_game_binary_is_dot_app = "L'exécutable cible est un répertoire, choisissez plutôt le fichier binaire à l'intérieur du « .app » comme cible !";
	error_game_binary_not_found = "Impossible de trouver le fichier binaire du jeu : « {0} »";
	error_game_binary_not_found_wine = "Impossible de trouver le fichier binaire du jeu (Wine)";
	error_game_security_violation = "Violation de la sécurité du jeu {0}";
	error_generic_ex = "Erreur inattendue. {0}";
	error_instance_count_limit = "Nombre maximum d'instances de jeu simultanées atteint !";
	error_internal = "Erreur interne !";
	error_invalid_executable_path = "Chemin d'accès de l'exécutable du jeu non valide !";
	error_memory_ex = "Mémoire insuffisante pour lancer le jeu {0}";
	error_missing_binary_path = "Chemin d'accès de l'exécutable du jeu manquant.";
	error_missing_directory_path = "Chemin d'accès du répertoire de travail manquant.";
	error_module_initialize = "Échec de l'initialisation du module avec {0}";
	error_module_loading = "Échec du chargement du module anti-cheat.";
	error_set_environment_variables = "Échec de définition des variables d'environnement pour le processus du jeu.";
	error_unrecognized_blacklisted_driver = "N/A a été détecté. Veuillez le décharger puis réessayer.";
	error_unsupported_machine_arch = "Architecture de machine hôte non prise en charge. ({0})";
	error_working_directory_not_found = "Le répertoire de travail n'existe pas.";
	error_x8664_required = "SE non pris en charge. Version 64 bits (x86-64) de Windows requise.";
	warn_module_download_size = "Taille de la réponse HTTP : {0}. Démarrage en mode client null.";
	warn_vista_deprecation = "Easy Anti-Cheat doit mettre fin à la prise en charge de Windows Vista en octobre 2020, car il n'est plus possible de créer des signatures de code compatibles. Consultez https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Impossible de valider la configuration anti-triche.";
	warn_win7_update_required = "Veuillez lancer les mises à jour de Windows, il manque à votre système la prise en charge de la signature de code SHA-2 obligatoire avant octobre 2020. Consultez https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "La mise à jour de service d'Easy Anti-Cheat a expiré."
};
setup_error:
{
	error_cancelled = "Opération annulée par l'utilisateur";
	error_encrypted = "Le dossier d'installation Easy Anti-Cheat a été chiffré";
	error_intro = "Échec de la configuration d'Easy Anti-Cheat";
	error_not_installed = "Easy Anti-Cheat n'est pas installé.";
	error_registry = "Échec de la copie du fichier exécutable du service";
	error_rights = "Privilèges insuffisants";
	error_service = "Création du service impossible";
	error_service_ex = "Création du service impossible {0}";
	error_system = "Accès à System32 refusé";
};