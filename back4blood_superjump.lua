
-- Back 4 Blood Super Jump Script
-- Load this in Cheat Engine

function findJumpVelocity()
    local process = getOpenedProcessID()
    if process == 0 then
        showMessage("Please attach to Back4Blood.exe first!")
        return
    end
    
    -- Search for common jump velocity values
    local memScan = createMemScan()
    memScan.firstScan(soExactValue, vtFloat, rtTruncated, "420", "", 0x00000000, 0x7FFFFFFF, "+W", fsmNotAligned, "4", false, false, false, false)
    memScan.waitTillDone()
    
    local foundList = createFoundList(memScan)
    foundList.initialize()
    
    if foundList.Count > 0 then
        showMessage("Found " .. foundList.Count .. " potential addresses. Check the results!")
        
        -- Add first few results to address list
        for i = 0, math.min(foundList.Count-1, 9) do
            local memrec = getAddressList().createMemoryRecord()
            memrec.Address = foundList[i]
            memrec.Type = vtFloat
            memrec.Description = "Jump Velocity " .. i
        end
    else
        showMessage("No jump velocity found. Try jumping in-game first!")
    end
    
    foundList.destroy()
    memScan.destroy()
end

function applySuperJump(multiplier)
    local addressList = getAddressList()
    
    for i = 0, addressList.Count-1 do
        local memrec = addressList[i]
        if memrec.Description:find("Jump Velocity") then
            local currentValue = memrec.Value
            if currentValue and tonumber(currentValue) then
                local newValue = tonumber(currentValue) * multiplier
                memrec.Value = tostring(newValue)
                print("Modified " .. memrec.Description .. ": " .. currentValue .. " -> " .. newValue)
            end
        end
    end
end

-- Create GUI
local form = createForm()
form.Caption = "Back 4 Blood Super Jump"
form.Width = 300
form.Height = 200

local btnFind = createButton(form)
btnFind.Caption = "Find Jump Velocity"
btnFind.Left = 10
btnFind.Top = 10
btnFind.Width = 120
btnFind.OnClick = findJumpVelocity

local btn3x = createButton(form)
btn3x.Caption = "3x Super Jump"
btn3x.Left = 140
btn3x.Top = 10
btn3x.Width = 120
btn3x.OnClick = function() applySuperJump(3) end

local btn5x = createButton(form)
btn5x.Caption = "5x Super Jump"
btn5x.Left = 10
btn5x.Top = 50
btn5x.Width = 120
btn5x.OnClick = function() applySuperJump(5) end

local btn10x = createButton(form)
btn10x.Caption = "10x Super Jump"
btn10x.Left = 140
btn10x.Top = 50
btn10x.Width = 120
btn10x.OnClick = function() applySuperJump(10) end

local btnRestore = createButton(form)
btnRestore.Caption = "Restore Normal"
btnRestore.Left = 75
btnRestore.Top = 90
btnRestore.Width = 120
btnRestore.OnClick = function() applySuperJump(1/3) end

local lblInstructions = createLabel(form)
lblInstructions.Caption = "1. Attach to Back4Blood.exe\n2. Jump in-game\n3. Click 'Find Jump Velocity'\n4. Use super jump buttons"
lblInstructions.Left = 10
lblInstructions.Top = 130
lblInstructions.Width = 280
lblInstructions.Height = 60

form.show()
