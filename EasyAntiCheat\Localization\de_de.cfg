#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "Suche online nach einer Lösung und schließe das Spiel.";
	btn_exit = "Schließe das Spiel.";
	btn_hidedetails = "Details ausblenden";
	btn_showdetails = "Details einblenden";
	chk_sendreport = "Fehlerbericht senden";
	error_code = "Fehlercode:";
	lbl_body1 = "Leider gab es ein Problem beim Starten deines Spiels";
	lbl_body2 = "Bitte hilf uns, indem du dieses Problem meldest.";
	lbl_body3 = "Easy Anti-Cheat kann online nach einer Lösung für dieses Problem suchen und versuchen, es zu beheben.";
	lbl_header = "Spiel konnte nicht gestartet werden";
	title = "Startfehler";
};
game_error:
{
	error_catalogue_corrupted = "Hash-Liste von Easy Anti-Cheat beschädigt";
	error_catalogue_not_found = "EAC-Index nicht gefunden";
	error_certificate_revoked = "EAC-Indexzertifikat wurde aufgehoben";
	error_corrupted_memory = "Fehlerhafter Speicher";
	error_corrupted_network = "Fehlerhafter Packet Flow";
	error_file_forbidden = "Unbekannte Spieldatei";
	error_file_not_found = "Erforderliche Datei fehlt";
	error_file_version = "Unbekannte Dateiversion";
	error_module_forbidden = "Verbotenes Modul";
	error_system_configuration = "Verbotene Systemkonfiguration";
	error_system_version = "Nicht vertrauenswürdige Systemdatei";
	error_tool_forbidden = "Verbotenes Tool";
	error_violation = "Interner Anti-Cheat-Fehler";
	error_virtual = "Läuft nicht auf virtueller Maschine";
	peer_client_banned = "Anti-Cheat Peer gesperrt.";
	peer_heartbeat_rejected = "Anti-Cheat Peer abgelehnt.";
	peer_validated = "Anti-Cheat Peerüberprüfung abgeschlossen.";
	peer_validation_failed = "Anti-Cheat Peerüberprüfung fehlgeschlagen";
	executable_not_hashed = "Für die ausführbare Spieldatei konnte kein Eintrag im Katalog gefunden werden.";
};
launcher:
{
	btn_cancel = "Abbrechen";
	btn_exit = "Verlassen";
	error_cancel = "Start abgebrochen";
	error_filenotfound = "Datei nicht gefunden";
	error_init = "Initialisierungsfehler";
	error_install = "Installationsfehler";
	error_launch = "Startfehler";
	error_nolib = "Easy Anti-Cheat-Bibliothek konnte nicht geladen werden";
	loading = "LÄDT";
	wait = "Bitte warten";
	initializing = "INITIALISIERT";
	success_waiting_for_game = "WARTET AUF DAS SPIEL";
	success_closing = "Erfolgreich";
	network_error = "Netzwerkfehler";
	error_no_settings_file = "{0} konnte nicht gefunden werden.";
	error_invalid_settings_format = "{0} hat kein gültiges JSON-Format.";
	error_missing_required_field = "In {0} fehlt ein erforderliches Feld ({1}).";
	error_invalid_eos_identifier = "{0} enthält eine ungültige EOS-Kennzeichnung: ({1})";
	download_progress = "Download-Fortschritt: {0}";
};
launcher_error:
{
	error_already_running = "Eine Anwendung mit AntiEasyCheat läuft bereits! {0}";
	error_application = "Der Game-Client ist auf einen Anwendungsfehler gestoßen. Fehlercode: {0}";
	error_bad_exe_format = "64-Bit-Betriebssystem erforderlich";
	error_bitset_32 = "Bitte verwende die Version 32-bit des Spiels";
	error_bitset_64 = "Bitte verwende die Version 64-bit des Spiels";
	error_cancelled = "Vorgang vom Benutzer abgebrochen";
	error_certificate_validation = "Fehler bei Validierung des Easy Anti-Cheat-Codesignierungs-Zertifikats";
	error_connection = "Verbindung mit dem Content-Verteilernetzwerk fehlgeschlagen!";
	error_debugger = "Ein Debugger wurde entdeckt. Bitte beende ihn und versuche es noch einmal.";
	error_disk_space = "Nicht genug Platz auf der Festplatte.";
	error_dns = "DNS-Auflösung vom Content-Verteilernetzwerk fehlgeschlagen! ";
	error_dotlocal = "DotLocal DLL-Umleitung entdeckt.";
	error_dotlocal_instructions = "Bitte lösche die folgende Datei";
	error_file_not_found = "Datei nicht gefunden:";
	error_forbidden_tool = "Bitte schließe {0}, bevor du das Spiel startest";
	error_forbidden_driver = "Bitte {0} vor dem Start des Spiels entfernen.";
	error_generic = "Unerwarteter Fehler.";
	error_kernel_debug = "Easy Anti-Cheat kann nicht ausgeführt werden, wenn Kernel-Debugging aktiviert ist";
	error_kernel_dse = "Easy Anti-Cheat kann nicht ausgeführt werden, wenn der Driver Signature Enforcement deaktiviert wurde.";
	error_kernel_modified = "Verbotene Windows-Kernel-Veränderung entdeckt";
	error_library_load = "Easy Anti-Cheat-Bibliothek konnte nicht geladen werden";
	error_memory = "Nicht genug Speicher, um das Spiel zu starten";
	error_module_load = "Anti-Cheat-Modul konnte nicht geladen werden";
	error_patched = "Ein gepatchter Windows Boot-Loader wurde entdeckt";
	error_process = "Vorgang kann nicht erstellte werden";
	error_process_crash = "Der Vorgang wurde plötzlich abgebrochen";
	error_safe_mode = "Easy Anti-Cheat kann nicht im Sicheren Modus von Windows ausgeführt werden.";
	error_socket = "Etwas hält die Anwendung davon ab, auf das Internet zuzugreifen!";
	error_ssl = "Fehler beim Herstellen der SSL-Verbindung mit dem CDN-Service!";
	error_start = "Spiel konnte nicht gestartet werden";
	error_uncpath_forbidden = "Das Spiel kann nicht über eine Netzwerkfreigabe ausgeführt werden. (UNC-Pfad)";
	error_connection_failed = "Verbindung fehlgeschlagen: ";
	error_missing_game_id = "Fehlende Spiel-ID";
	error_dns_resolve_failed = "DNS konnte den Proxy-Server-Namen nicht auflösen";
	error_dns_connection_failed = "Verbindung zum Content Distribution Network fehlgeschlagen! Curl-Code: {0}!";
	error_http_response = "HTTP-Antwort-Code: {0} Curl-Code: {1}";
	error_driver_handle = "Unerwarteter Fehler. (Treiber-Handle konnte nicht geöffnet werden)";
	error_incompatible_service = "Eine inkompatibler Easy Anti-Cheat-Dienst wird bereits ausgeführt. Schließe bitte andere Spiele oder starte deinen PC neu.";
	error_incompatible_driver_version = "Eine inkompatible Treiberversion von Easy Anti-Cheat wird bereits ausgeführt. Schließe bitte andere Spiele oder starte deinen PC neu.";
	error_another_launcher = "Unerwarteter Fehler. (Ein anderer Launcher wird derzeit ausgeführt)";
	error_game_running = "Unerwarteter Fehler. (Spiel wird bereits ausgeführt)";
	error_patched_boot_loader = "Gepatchtes Windows-Startladeprogramm erkannt. (Kernel-Patch-Schutz deaktiviert)";
	error_unknown_process = "Game-Client nicht erkannt Fortfahren nicht möglich.";
	error_unknown_game = "Nicht konfiguriertes Spiel. Fortfahren nicht möglich.";
	error_win7_required = "Du benötigst Windows 7 oder höher.";
	success_initialized = "Easy Anti-Cheat erfolgreich gestartet";
	success_loaded = "Easy Anti-Cheat erfolgreich im Spiel geladen";
	error_create_process = "Spielprozess konnte nicht erstellt werden: {0}";
	error_create_thread = "Hintergrund-Thread konnte nicht erstellt werden!";
	error_disallowed_cdn_path = "Unerwarteter Fehler. (Falsche CDN-URL)";
	error_empty_executable_field = "Der Dateipfad zur Binary-Datei des Spiels wurde nicht angegeben.";
	error_failed_path_query = "Pfad des Prozesses konnte nicht abgerufen werden.";
	error_failed_to_execute = "Der Spielprozess konnte nicht ausgeführt werden.";
	error_game_binary_is_directory = "Die ausführbare Zieldatei ist ein Verzeichnis!";
	error_game_binary_is_dot_app = "Die ausführbare Zieldatei ist ein Verzeichnis. Wähle die Binary-Datei innerhalb der .app-Datei aus!";
	error_game_binary_not_found = "Die Binary-Datei des Spiels („{0}“) konnte nicht gefunden werden.";
	error_game_binary_not_found_wine = "Die Binary-Datei des Spiels konnte nicht gefunden werden (Wine).";
	error_game_security_violation = "Spielsicherheitsverletzung {0}";
	error_generic_ex = "Unerwarteter Fehler. {0}";
	error_instance_count_limit = "Maximale Anzahl gleichzeitiger Spielinstanzen erreicht!";
	error_internal = "Interner Fehler!";
	error_invalid_executable_path = "Ungültiger Pfad zur ausführbaren Spieldatei!";
	error_memory_ex = "Ungenügender Speicher, um das Spiel {0} zu starten.";
	error_missing_binary_path = "Fehlender Pfad zur ausführbaren Spieldatei.";
	error_missing_directory_path = "Fehlender Pfad zum Arbeitsverzeichnis.";
	error_module_initialize = "Modul-Initialisierung mit {0} fehlgeschlagen.";
	error_module_loading = "Anti-Cheat-Modul konnte nicht geladen werden.";
	error_set_environment_variables = "Umgebungsvariablen für den Spielprozess konnten nicht gesetzt werden.";
	error_unrecognized_blacklisted_driver = "N/A wurde entdeckt. Bitte entlade es und versuche es erneut.";
	error_unsupported_machine_arch = "Die Rechnerarchitektur des Hosts wird nicht unterstützt. ({0})";
	error_working_directory_not_found = "Das Arbeitsverzeichnis existiert nicht.";
	error_x8664_required = "Das Betriebssystem wird nicht unterstützt. Eine 64-Bit-Windows-Version (x86-64) ist erforderlich.";
	warn_module_download_size = "HTTP-Antwortgröße: {0}. Es wird im Null-Client-Modus gestartet.";
	warn_vista_deprecation = "Easy Anti-Cheat muss ab Oktober 2020 die Unterstützung für Windows Vista einstellen, da keine kompatiblen Codesignaturen mehr erstellt werden können. Siehe: https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Die Anti-Cheat-Konfiguration konnte nicht überprüft werden.";
	warn_win7_update_required = "Bitte aktualisiere Windows. Dein System verfügt nicht über die ab Oktober 2020 notwendige SHA-2-Codesignierungsunterstützung. Siehe: https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Zeitüberschreitung bei der Aktualisierung des Easy Anti-Cheat-Dienstes."
	error_launch_ex = "Startfehler: {0}";
};
setup:
{
	btn_finish = "Beenden";
	btn_install = "Jetzt installieren";
	btn_repair = "Dienst reparieren";
	btn_uninstall = "Deinstallieren";
	epic_link = "© Epic Games, Inc";
	install_progress = "Installiert …";
	install_success = "Erfolgreich installiert";
	licenses_link = "Lizenzen";
	privacy_link = "Datenschutz";
	repair_progress = "Repariert ...";
	title = "Easy Anti-Cheat Service-Einrichtung";
	uninstall_progress = "Deinstalliert …";
	uninstall_success = "Erfolgreich deinstalliert";
};
setup_error:
{
	error_cancelled = "Vorgang vom Benutzer abgebrochen";
	error_encrypted = "Der Easy Anti-Cheat-Installationsordner wurde verschlüsselt";
	error_intro = "Easy Anti-Cheat-Einrichtung fehlgeschlagen";
	error_not_installed = "Easy Anti-Cheat ist nicht installiert.";
	error_registry = "Kopieren der ausführbaren Dienstdatei fehlgeschlagen";
	error_rights = "Rechte nicht ausreichend";
	error_service = "Service kann nicht erstellt werden";
	error_service_ex = "Service kann nicht erstellt werden {0}";
	error_system = "Zugriff auf System32 verweigert";
};