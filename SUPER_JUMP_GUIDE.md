# Back 4 Blood Super Jump Mod Guide

## ⚠️ Important Notes
- **OFFLINE SINGLE PLAYER ONLY** - Do not use in online multiplayer
- Always backup your save files before modifying anything
- These modifications are temporary and reset when you restart the game

## Method 1: Using Cheat Engine (Recommended for Beginners)

### Prerequisites
1. Download and install [Cheat Engine](https://cheatengine.org/)
2. Make sure Back 4 Blood is installed and working

### Step-by-Step Instructions

1. **Start the Game**
   - Launch Back 4 Blood
   - Go to **Solo Campaign** or **Training** mode (offline only)
   - Load into any level where you can move around

2. **Open Cheat Engine**
   - Run Cheat Engine as Administrator
   - Click the computer icon (top-left) to select a process
   - Choose "Back4Blood.exe" from the list
   - Click "Open"

3. **Find Jump Velocity Value**
   - In the game, try jumping and note how high you go
   - In Cheat Engine, set "Value Type" to "Float"
   - Search for values between 400-800 (typical jump velocity range)
   - Jump in the game, then search for "Changed value"
   - Repeat until you find 1-3 addresses

4. **Test the Address**
   - Double-click the found address to add it to the bottom panel
   - Change the value to something higher (try 1200 for 3x jump)
   - Test in-game - you should jump much higher!

5. **Fine-tune Your Super Jump**
   - Normal jump velocity: ~420
   - 2x Super Jump: ~840
   - 3x Super Jump: ~1260
   - 5x Super Jump: ~2100
   - 10x Super Jump: ~4200

### Using the Provided Cheat Table
1. Load the `cheat_engine_super_jump.ct` file in Cheat Engine
2. Follow the search process above to find the base address
3. Use the preset multipliers provided

## Method 2: Using Python Script (Advanced)

### Prerequisites
```bash
pip install psutil
```

### Usage
1. Start Back 4 Blood in offline mode
2. Run the Python script: `python super_jump_mod.py`
3. Follow the on-screen instructions

**Note:** The Python script requires you to find the memory address first using Cheat Engine or similar tools.

## Method 3: Memory Address Approach (Expert Level)

If you want to find the exact memory addresses using the forum information:

### Using the Provided Offsets
From the UnknownCheats thread:
- GNames: 0x6986c80
- GObjects: 0x667c778
- GWorld: 0x69737B0
- GObjects xor key: 0x8375

### Finding Player Movement Component
1. Use the GWorld address to find the current level
2. Navigate to the player character object
3. Find the CharacterMovementComponent
4. Look for JumpZVelocity property (usually around offset +0x300)

## Troubleshooting

### Game Won't Start / Crashes
- Make sure you're only playing offline
- Verify game files through Steam
- Try running both the game and Cheat Engine as Administrator

### Can't Find Jump Velocity
- Try searching for different value ranges (300-1000)
- Make sure you're jumping when doing "Changed value" searches
- Look for multiple addresses and test each one

### Super Jump Too High/Low
- Adjust the multiplier values
- Normal jump is usually around 420 units
- Start with small increases (600-800) and work up

### Values Reset After Death/Level Change
- This is normal behavior
- You'll need to reapply the modification
- Consider using Cheat Engine's hotkeys for quick toggling

## Safety Tips

1. **Always play offline** - Never use these modifications in online multiplayer
2. **Backup saves** - Copy your save files before experimenting
3. **Start small** - Begin with 2x-3x multipliers before going extreme
4. **Test in safe areas** - Make sure you won't get stuck or fall off the map

## Advanced Modifications

Once you're comfortable with super jump, you can also modify:
- **Movement Speed**: Look for MaxWalkSpeed values
- **Air Control**: Modify AirControl for better mid-air movement
- **Fall Damage**: Reduce or eliminate fall damage
- **Gravity**: Change the gravity scale for floating effects

## Legal Disclaimer

These modifications are for educational and personal entertainment purposes only. Use only in offline single-player mode. The authors are not responsible for any issues that may arise from using these modifications.

## Need Help?

If you run into issues:
1. Make sure you're following the offline-only rule
2. Check that Cheat Engine is running as Administrator
3. Verify the game process is properly attached
4. Try restarting both the game and Cheat Engine

Happy jumping! 🚀
