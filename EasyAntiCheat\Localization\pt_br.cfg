#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "Acesse a internet para procurar uma solução e feche o jogo.";
	btn_exit = "Fechar o jogo.";
	btn_hidedetails = "Ocultar detalhes";
	btn_showdetails = "Exibir detalhes";
	chk_sendreport = "Enviar relatório de erro";
	error_code = "Código de Erro:";
	lbl_body1 = "Infelizmente tivemos um problema na inicialização do seu jogo";
	lbl_body2 = "Por favor, ajude-nos descrevendo este problema.";
	lbl_body3 = "O Easy Anti-Cheat pode procurar uma solução online para o problema para ajudar em sua resolução.";
	lbl_header = "Não foi possível iniciar o jogo";
	title = "Erro na Execução";
};
game_error:
{
	error_catalogue_corrupted = "Catálogo de hash do Easy Anti-Cheat corrompido";
	error_catalogue_not_found = "Índice EAC não encontrado";
	error_certificate_revoked = "Certificado de índice do EAC revogado";
	error_corrupted_memory = "Memória corrompida";
	error_corrupted_network = "Fluxo de pacotes corrompido";
	error_file_forbidden = "Arquivo de jogo desconhecido";
	error_file_not_found = "Arquivo necessário não encontrado";
	error_file_version = "Versão do arquivo desconhecida";
	error_module_forbidden = "Módulo proibido";
	error_system_configuration = "Configuração de sistema proibida";
	error_system_version = "Arquivo de sistema não confiável";
	error_tool_forbidden = "Ferramenta proibida";
	error_violation = "Erro interno de anti-trapaça";
	error_virtual = "Não é possível executar em máquina virtual.";
	peer_client_banned = "Ponto antitrapaças banido.";
	peer_heartbeat_rejected = "Ponto antitrapaças rejeitado.";
	peer_validated = "Validação do ponto antitrapaças concluída.";
	peer_validation_failed = "Falha na validação do ponto antitrapaças.";
	executable_not_hashed = "Não foi possível localizar a entrada executável do jogo no catálogo.";
};
launcher:
{
	btn_cancel = "Cancelar";
	btn_exit = "Sair";
	error_cancel = "Execução Cancelada";
	error_filenotfound = "Arquivo Não Encontrado";
	error_init = "Erro de Inicialização";
	error_install = "Erro na Instalação";
	error_launch = "Erro na Execução";
	error_nolib = "Não foi possível carregar a biblioteca do Easy Anti-Cheat";
	loading = "CARREGANDO";
	wait = "Por favor, aguarde";
	initializing = "INICIALIZANDO";
	success_waiting_for_game = "AGUARDANDO JOGO";
	success_closing = "Sucesso";
	network_error = "Erro de rede";
	error_no_settings_file = "{0} não encontrado";
	error_invalid_settings_format = "{0} não apresenta formato JSON válido";
	error_missing_required_field = "{0} não apresenta um dos campos obrigatórios ({1})";
	error_invalid_eos_identifier = "{0} contém um identificador EOS inválido: ({1})";
	download_progress = "Andamento do download: {0}";
};
launcher_error:
{
	error_already_running = "Um aplicativo em execução já está usando o Easy Anti-Cheat! {0}";
	error_application = "O cliente do jogo encontrou um erro no aplicativo. Código de Erro: {0}";
	error_bad_exe_format = "SO de 64 bits necessário";
	error_bitset_32 = "Por favor, use a versão 32-bit do jogo";
	error_bitset_64 = "Por favor, use a versão 64-bit do jogo";
	error_cancelled = "Operação cancelada pelo usuário";
	error_certificate_validation = "Erro de validação do código do certificado de assinatura do Easy Anti-Cheat";
	error_connection = "Falha na conexão com a Rede de Distribuição de Conteúdo!";
	error_debugger = "Debugger detectado. Por favor, descarregue-o e tente novamente";
	error_disk_space = "Espaço em disco insuficiente.";
	error_dns = "Falha na resolução de DNS para a Rede de Distribuição de Conteúdo!";
	error_dotlocal = "Redirecionamento do DLL DotLocal detectado.";
	error_dotlocal_instructions = "Por favor, exclua o arquivo a seguir";
	error_file_not_found = "Arquivo não encontrado:";
	error_forbidden_tool = "Por favor, feche {0} antes de iniciar o jogo";
	error_forbidden_driver = "Descarregue {0} antes de iniciar o jogo";
	error_generic = "Erro inesperado.";
	error_kernel_debug = "O Easy Anti-Cheat não pode ser executado se o Kernel Debugging estiver ativo";
	error_kernel_dse = "O Easy Anti-Cheat não pode ser executado se Driver Signature Enforcement for desativado";
	error_kernel_modified = "Modificação proibida do kernel do Windows detectada";
	error_library_load = "Não foi possível carregar a biblioteca do Easy Anti-Cheat";
	error_memory = "Memória insuficiente para iniciar o jogo";
	error_module_load = "Falha ao carregar o módulo anti-trapaça";
	error_patched = "Modificação do boot loader do Windows detectada";
	error_process = "Não foi possível criar o processo";
	error_process_crash = "O processo foi encerrado inesperadamente";
	error_safe_mode = "O Easy Anti-Cheat não pode ser executado no Modo de Segurança do Windows";
	error_socket = "Algo está bloqueando o acesso à Internet para o aplicativo!";
	error_ssl = "Erro ao estabelecer conexão SSL com o serviço CDN!";
	error_start = "Falha ao iniciar o jogo";
	error_uncpath_forbidden = "Não é possível rodar o jogo por um compartilhamento de rede. (caminho UNC).";
	error_connection_failed = "Falha na conexão: ";
	error_missing_game_id = "Id do jogo ausente";
	error_dns_resolve_failed = "Falha na resolução do DNS para proxy";
	error_dns_connection_failed = "Falha na conexão à Rede de Distribuição de Conteúdo! Código Curl: {0}!";
	error_http_response = "Código de Resposta HTTP: {0} Código Curl: {1}";
	error_driver_handle = "Erro inesperado. (Não foi possível abrir o identificador do driver)";
	error_incompatible_service = "Uma versão incompatível do serviço Easy Anti-Cheat já está sendo executada. Saia dos outros jogos em execução ou reinicie.";
	error_incompatible_driver_version = "Uma versão incompatível do driver do Easy Anti-Cheat já está sendo executada. Saia dos outros jogos em execução ou reinicie.";
	error_another_launcher = "Erro inesperado. (Outro inicializador já está sendo executado)";
	error_game_running = "Erro inesperado. (O jogo já está sendo executado)";
	error_patched_boot_loader = "Carregador de inicialização do Windows corrigido detectado. (Proteção contra Patches do Kernel desativada)";
	error_unknown_process = "Cliente de jogo não reconhecido Não é possível continuar.";
	error_unknown_game = "Jogo não configurado. Não é possível continuar.";
	error_win7_required = "Exige Windows 7 ou mais atual.";
	success_initialized = "O Easy Anti-Cheat foi iniciado com sucesso";
	success_loaded = "O Easy Anti-Cheat foi carregado com sucesso no jogo";
	error_create_process = "Não foi possível criar o processo do jogo: {0}";
	error_create_thread = "Não foi possível criar a thread em segundo plano!";
	error_disallowed_cdn_path = "Erro inesperado. (URL do CDN incorreto)";
	error_empty_executable_field = "O caminho do binário do jogo não foi fornecido.";
	error_failed_path_query = "Não foi possível identificar o caminho do processo";
	error_failed_to_execute = "Não foi possível executar o processo do jogo.";
	error_game_binary_is_directory = "O alvo executável é uma pasta!";
	error_game_binary_is_dot_app = "O alvo executável é uma pasta, ao invés disso, use como alvo o binário dentro de .app!";
	error_game_binary_not_found = "Não foi possível localizar o binário do jogo: '{0}'";
	error_game_binary_not_found_wine = "Não foi possível localizar o binário do jogo (Wine)";
	error_game_security_violation = "Violação da segurança do jogo {0}";
	error_generic_ex = "Erro inesperado. {0}";
	error_instance_count_limit = "O número máximo de instâncias simultâneas do jogo foi atingido!";
	error_internal = "Erro interno!";
	error_invalid_executable_path = "Caminho do executável do jogo inválido!";
	error_memory_ex = "Memória insuficiente para iniciar o jogo {0}";
	error_missing_binary_path = "Caminho do executável do jogo ausente.";
	error_missing_directory_path = "Caminho da pasta de trabalho ausente.";
	error_module_initialize = "A inicialização do módulo falhou com {0}";
	error_module_loading = "Não foi possível carregar o módulo antitrapaça.";
	error_set_environment_variables = "Não foi possível determinar as variáveis do ambiente para o processo do jogo.";
	error_unrecognized_blacklisted_driver = "N/A foi detectado. Descarregue e tente novamente.";
	error_unsupported_machine_arch = "Não há suporte para a arquitetura da máquina hospedeira. ({0})";
	error_working_directory_not_found = "A pasta de trabalho não existe.";
	error_x8664_required = "SO não suportado. É necessário ter a versão de 64 bits (x86-64) do Windows.";
	warn_module_download_size = "Tamanho da resposta HTTP: {0}. Iniciando no modo de cliente inexistente.";
	warn_vista_deprecation = "O Easy Anti-Cheat precisará encerrar o suporte para Windows Vista em outubro de 2020 porque as assinaturas de código compatíveis não podem mais ser criadas. Saiba mais em https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Não foi possível validar a configuração do antitrapaça.";
	warn_win7_update_required = "Atualize o Windows, seu sistema está sem o suporte essencial para assinatura de código SHA-2, que precisa estar presente até outubro de 2020. Saiba mais em https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "A atualização do serviço Easy Anti-Cheat expirou."
	error_launch_ex = "Erro na Execução: {0}";
};
setup:
{
	btn_finish = "Finalizar";
	btn_install = "Instalar Agora";
	btn_repair = "Serviço de Reparos";
	btn_uninstall = "Desinstalar";
	epic_link = "© Epic Games, Inc";
	install_progress = "Instalando...";
	install_success = "Instalação Concluída";
	licenses_link = "Licenças";
	privacy_link = "Privacidade";
	repair_progress = "Reparando...";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "Desinstalando...";
	uninstall_success = "Desinstalação Concluída";
};
setup_error:
{
	error_cancelled = "Operação cancelada pelo usuário";
	error_encrypted = "A pasta de instalação do Easy Anti-Cheat está criptografada";
	error_intro = "Falha na Configuração do Easy Anti-Cheat";
	error_not_installed = "Easy Anti-Cheat não instalado.";
	error_registry = "Falha na cópia do serviço executável";
	error_rights = "Privilégios insuficientes";
	error_service = "Não foi possível criar o serviço";
	error_service_ex = "Não foi possível criar o serviço {0}";
	error_system = "Acesso a System32 recusado";
};