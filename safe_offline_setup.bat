@echo off
echo ========================================
echo Back 4 Blood Safe Offline Setup
echo ========================================
echo.
echo This script will help you set up offline play
echo without EasyAntiCheat interference.
echo.
echo WARNING: Only use for offline single-player!
echo.
pause

echo Checking current setup...
if exist "EasyAntiCheat" (
    echo EasyAntiCheat folder found.
    echo.
    echo Choose an option:
    echo 1. Temporarily disable EAC for offline play
    echo 2. Keep EAC enabled (safer but no mods)
    echo 3. Exit
    echo.
    set /p choice="Enter your choice (1-3): "
    
    if "%choice%"=="1" (
        echo.
        echo Creating backup and disabling EAC...
        if not exist "EasyAntiCheat_backup" (
            xcopy "EasyAntiCheat" "EasyAntiCheat_backup\" /E /I /H /Y
            echo Backup created: EasyAntiCheat_backup
        )
        
        ren "EasyAntiCheat" "EasyAntiCheat_disabled"
        echo EasyAntiCheat temporarily disabled.
        echo.
        echo You can now run the game in offline mode without EAC.
        echo To re-enable EAC later, run this script again.
        echo.
    ) else if "%choice%"=="2" (
        echo Keeping EAC enabled. No modifications will work.
    ) else (
        echo Exiting...
        exit /b
    )
) else if exist "EasyAntiCheat_disabled" (
    echo EasyAntiCheat is currently disabled.
    echo.
    echo Choose an option:
    echo 1. Re-enable EAC (for online play)
    echo 2. Keep disabled (for offline mods)
    echo 3. Exit
    echo.
    set /p choice="Enter your choice (1-3): "
    
    if "%choice%"=="1" (
        echo.
        echo Re-enabling EasyAntiCheat...
        ren "EasyAntiCheat_disabled" "EasyAntiCheat"
        echo EasyAntiCheat re-enabled.
        echo You can now play online safely.
        echo.
    ) else if "%choice%"=="2" (
        echo EAC remains disabled for offline play.
    ) else (
        echo Exiting...
        exit /b
    )
) else (
    echo EasyAntiCheat folder not found. This may not be the correct directory.
    echo Make sure you're running this from the Back 4 Blood game folder.
)

echo.
echo ========================================
echo IMPORTANT REMINDERS:
echo ========================================
echo - Only play OFFLINE when EAC is disabled
echo - Re-enable EAC before playing online
echo - Never attempt to join online games with EAC disabled
echo - This is for educational/single-player fun only
echo ========================================
echo.
pause
