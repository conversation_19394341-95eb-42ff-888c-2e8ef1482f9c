bugreport:
{
	btn_continue = "Go online for a solution and close the game.";
	btn_exit = "Close the game.";
	btn_hidedetails = "Hide details";
	btn_showdetails = "Show details";
	chk_sendreport = "Send error report";
	error_code = "Error Code:";
	lbl_body1 = "We're sorry, we had a problem starting your game";
	lbl_body2 = "Please help us by reporting this issue.";
	lbl_body3 = "Easy Anti-Cheat can check online for a solution for the problem and try to help in resolving it.";
	lbl_header = "Couldn't start the game";
	title = "Launch Error";
};
game_error:
{
	error_catalogue_corrupted = "Corrupt Easy Anti-Cheat Hash Catalogue";
	error_catalogue_not_found = "Easy Anti-Cheat Hash Catalogue not found";
	error_certificate_revoked = "EAC index certificate revoked";
	error_corrupted_memory = "Corrupted memory";
	error_corrupted_network = "Corrupted packet flow";
	error_file_forbidden = "Unknown game file";
	error_file_not_found = "Missing required file";
	error_file_version = "Unknown file version";
	error_module_forbidden = "Forbidden module";
	error_system_configuration = "Forbidden system configuration";
	error_system_version = "Untrusted system file";
	error_tool_forbidden = "Forbidden tool";
	error_violation = "Internal anti-cheat error";
	error_virtual = "Cannot run under Virtual Machine.";
	peer_client_banned = "Anti-cheat peer banned.";
	peer_heartbeat_rejected = "Anti-cheat peer rejected.";
	peer_validated = "Anti-cheat peer validation completed.";
	peer_validation_failed = "Anti-cheat peer validation failed.";
	executable_not_hashed = "Could not locate game executable entry in the catalogue.";
};
launcher:
{
	btn_cancel = "Cancel";
	btn_exit = "Exit";
	error_cancel = "Launch Canceled";
	error_filenotfound = "File Not Found";
	error_init = "Initialization Error";
	error_install = "Install Error";
	error_launch = "Launch Error";
	error_nolib = "Could not load Easy Anti-Cheat library";
	loading = "LOADING";
	initializing = "INITIALIZING";
	wait = "Please wait";
	success_waiting_for_game = "WAITING FOR GAME";
	success_closing = "Success";
	network_error = "Network error";
	error_no_settings_file = "{0} not found";
	error_invalid_settings_format = "{0} does not have a valid JSON format";
	error_missing_required_field = "{0} is missing a required field ({1})";
	initializing = "INITIALIZING";
	network_error = "Network error";
	error_no_settings_file = "{0} not found";
	error_invalid_eos_identifier = "{0} contains an invalid EOS identifier: ({1})";
	download_progress = "Download Progress: {0}";
};
launcher_error:
{
	error_already_running = "An application using Easy Anti-Cheat is already running! {0}";
	error_application = "Game client encountered an application error. Error code: {0}";
	error_bad_exe_format = "64-bit OS required";
	error_bitset_32 = "Please use the 32-bit version of the game";
	error_bitset_64 = "Please use the 64-bit version of the game";
	error_cancelled = "Operation cancelled by user";
	error_connection = "Connecting to the Content Distribution Network failed!";
	error_debugger = "Debugger has been detected. Please unload it and try again";
	error_disk_space = "Insufficient disk space.";
	error_dns = "DNS resolve to the Content Distribution Network failed!";
	error_dotlocal = "DotLocal DLL redirection detected.";
	error_dotlocal_instructions = "Please delete the following file";
	error_file_not_found = "File not found:";
	error_forbidden_tool = "Please close {0} before starting the game";
	error_forbidden_driver = "Please unload {0} before starting the game";
	error_generic = "Unexpected error.";
	error_kernel_debug = "Easy Anti-Cheat cannot run if Kernel Debugging is enabled";
	error_kernel_dse = "Easy Anti-Cheat cannot run if Driver Signature Enforcement has been disabled";
	error_kernel_modified = "Forbidden Windows kernel modification detected";
	error_library_load = "Could not load Easy Anti-Cheat library";
	error_memory = "Insufficient memory to start the game";
	error_module_load = "Failed to load the anti-cheat module";
	error_patched = "Patched Windows boot loader detected";
	error_process = "Cannot create process";
	error_process_crash = "The process terminated abruptly";
	error_safe_mode = "Easy Anti-Cheat cannot run under Windows Safe Mode";
	error_socket = "Something is blocking the application from accessing Internet!";
	error_ssl = "Error establishing SSL connection with the CDN service!";
	error_start = "Failed to start the game";
	error_uncpath_forbidden = "Cannot run the game through a network share. (UNC path)";
	error_incompatible_driver_version = "An incompatible Easy Anti-Cheat driver version is already running. Please exit other running games or reboot";
	error_incompatible_service = "An incompatible Easy Anti-Cheat service is already running. Please exit other running games or reboot";
	error_connection_failed = "Connection failed: ";
	error_missing_game_id = "Missing game id!";
	error_dns_resolve_failed = "DNS resolve to proxy failed!";
	error_dns_connection_failed = "Connection to the Content Distribution Network failed! Curl Code: {0}!";
	error_http_response = "HTTP Response Code: {0} Curl Code: {1}";
	error_driver_handle = "Unexpected error. (Failed to open driver handle)";
	error_another_launcher = "Unexpected error. (Another launcher is already running)";
	error_game_running = "Unexpected error. (Game already running)";
	error_patched_boot_loader = "Patched Windows boot loader detected. (Kernel Patch Protection disabled)";
	error_dtrace_enabled = "Easy Anti-Cheat cannot run if Dynamic Tracing (DTrace) is enabled";
	error_disallowed_cdn_path = "Unexpected error. (Incorrect CDN url)";
	error_unknown_process = "Unrecognized game client. Cannot continue.";
	error_unknown_game = "Unconfigured game. Cannot continue.";
	error_win7_required = "Windows 7 or later is required.";
	error_x8664_required = "Unsupported OS. 64-bit (x86-64) version of Windows is required.";
	success_initialized = "Easy Anti-Cheat successfully initialized";
	success_loaded = "Easy Anti-Cheat successfully loaded in-game";
	warn_module_download_size = "HTTP Response Size: {0}. Starting in null client mode.";
	error_create_thread = "Failed at creating the background thread!";
	error_empty_executable_field = "The path to the game binary was not provided.";
	error_working_directory_not_found = "Working directory does not exist.";
	error_game_binary_not_found = "Could not locate the game binary: '{0}'";
	error_game_binary_not_found_wine = "Failed to locate the game binary (Wine)";
	error_game_binary_is_directory = "The target executable is a directory!";
	error_game_binary_is_dot_app = "The target executable is a directory, target the binary within the .app instead!";
	error_create_process = "Failed to create the game process: {0}";
	error_certificate_validation = "Error validating EasyAntiCheat code signing certificate.";
	error_module_loading = "Failed to load the anti-cheat module.";
	error_internal = "Internal error!";
	error_module_initialize = "Module initialization failed with {0}";
	error_failed_to_execute = "Failed to execute the game process.";
	error_instance_count_limit = "Maximum simultaneous game instances reached!";
	error_game_security_violation = "Game Security Violation {0}";
	error_failed_path_query = "Failed to get the process' path";
	error_set_environment_variables = "Failed to set environment variables for game process.";
	error_unsupported_machine_arch = "Unsupported host machine architecture. ({0})";
	error_memory_ex = "Insufficient memory to start the game {0}"
	error_unrecognized_blacklisted_driver = "N/A has been detected. Please unload it and try again.";
	error_generic_ex = "Unexpected error. {0}";
	error_invalid_executable_path = "Invalid game executable path!";
	error_missing_binary_path = "Missing game executable path.";
	error_missing_directory_path = "Missing working directory path.";
	error_configuration = "Could not validate the anti-cheat configuration.";
	warn_win7_update_required = "Please run Windows updates, your system is lacking critical SHA-2 code signing support required by October 2020. See https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Easy Anti-Cheat service update timed out."
	error_launch_ex = "Launch Error: {0}";
};
setup:
{
	btn_finish = "Finish";
	btn_install = "Install Easy Anti-Cheat";
	btn_repair = "Repair Service";
	btn_uninstall = "Uninstall";
	epic_link = "© Epic Games, Inc";
	install_progress = "Installing…";
	install_success = "Installed Successfully";
	licenses_link = "Licenses";
	privacy_link = "Privacy";
	repair_progress = "Repairing…";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "Uninstalling…";
	uninstall_success = "Uninstalled Successfully";
};
setup_error:
{
	error_cancelled = "Operation cancelled by user";
	error_encrypted = "Easy Anti-Cheat installation folder has been encrypted";
	error_intro = "Easy Anti-Cheat Setup Failed";
	error_not_installed = "Easy Anti-Cheat is not installed.";
	error_registry = "Access to registry denied";
	error_rights = "Insufficient privileges";
	error_service = "Cannot create service";
	error_service_ex = "Cannot create service {0}";
	error_system = "Access to System32 denied";
};