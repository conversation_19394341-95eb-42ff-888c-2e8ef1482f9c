#!/usr/bin/env python3
"""
Cheat Engine Automation for Back 4 Blood Super Jump
This will help you set up super jump using memory scanning
"""

import ctypes
import ctypes.wintypes
import struct
import time
import psutil
import sys
import os

class CheatEngineHelper:
    def __init__(self):
        self.process_handle = None
        self.game_pid = None
        
    def find_game(self):
        """Find Back 4 Blood process"""
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'back4blood' in proc.info['name'].lower():
                    self.game_pid = proc.info['pid']
                    print(f"✅ Found Back 4 Blood (PID: {self.game_pid})")
                    return True
            except:
                continue
        return False
    
    def create_cheat_engine_script(self):
        """Create a Cheat Engine Lua script for super jump"""
        script = '''
-- Back 4 Blood Super Jump Script
-- Load this in Cheat Engine

function findJumpVelocity()
    local process = getOpenedProcessID()
    if process == 0 then
        showMessage("Please attach to Back4Blood.exe first!")
        return
    end
    
    -- Search for common jump velocity values
    local memScan = createMemScan()
    memScan.firstScan(soExactValue, vtFloat, rtTruncated, "420", "", 0x00000000, 0x7FFFFFFF, "+W", fsmNotAligned, "4", false, false, false, false)
    memScan.waitTillDone()
    
    local foundList = createFoundList(memScan)
    foundList.initialize()
    
    if foundList.Count > 0 then
        showMessage("Found " .. foundList.Count .. " potential addresses. Check the results!")
        
        -- Add first few results to address list
        for i = 0, math.min(foundList.Count-1, 9) do
            local memrec = getAddressList().createMemoryRecord()
            memrec.Address = foundList[i]
            memrec.Type = vtFloat
            memrec.Description = "Jump Velocity " .. i
        end
    else
        showMessage("No jump velocity found. Try jumping in-game first!")
    end
    
    foundList.destroy()
    memScan.destroy()
end

function applySuperJump(multiplier)
    local addressList = getAddressList()
    
    for i = 0, addressList.Count-1 do
        local memrec = addressList[i]
        if memrec.Description:find("Jump Velocity") then
            local currentValue = memrec.Value
            if currentValue and tonumber(currentValue) then
                local newValue = tonumber(currentValue) * multiplier
                memrec.Value = tostring(newValue)
                print("Modified " .. memrec.Description .. ": " .. currentValue .. " -> " .. newValue)
            end
        end
    end
end

-- Create GUI
local form = createForm()
form.Caption = "Back 4 Blood Super Jump"
form.Width = 300
form.Height = 200

local btnFind = createButton(form)
btnFind.Caption = "Find Jump Velocity"
btnFind.Left = 10
btnFind.Top = 10
btnFind.Width = 120
btnFind.OnClick = findJumpVelocity

local btn3x = createButton(form)
btn3x.Caption = "3x Super Jump"
btn3x.Left = 140
btn3x.Top = 10
btn3x.Width = 120
btn3x.OnClick = function() applySuperJump(3) end

local btn5x = createButton(form)
btn5x.Caption = "5x Super Jump"
btn5x.Left = 10
btn5x.Top = 50
btn5x.Width = 120
btn5x.OnClick = function() applySuperJump(5) end

local btn10x = createButton(form)
btn10x.Caption = "10x Super Jump"
btn10x.Left = 140
btn10x.Top = 50
btn10x.Width = 120
btn10x.OnClick = function() applySuperJump(10) end

local btnRestore = createButton(form)
btnRestore.Caption = "Restore Normal"
btnRestore.Left = 75
btnRestore.Top = 90
btnRestore.Width = 120
btnRestore.OnClick = function() applySuperJump(1/3) end

local lblInstructions = createLabel(form)
lblInstructions.Caption = "1. Attach to Back4Blood.exe\\n2. Jump in-game\\n3. Click 'Find Jump Velocity'\\n4. Use super jump buttons"
lblInstructions.Left = 10
lblInstructions.Top = 130
lblInstructions.Width = 280
lblInstructions.Height = 60

form.show()
'''
        
        with open('back4blood_superjump.lua', 'w') as f:
            f.write(script)
        
        print("✅ Created Cheat Engine script: back4blood_superjump.lua")
        return True
    
    def create_manual_instructions(self):
        """Create step-by-step manual instructions"""
        instructions = """
🚀 BACK 4 BLOOD SUPER JUMP - MANUAL SETUP

STEP 1: Download Cheat Engine
- Go to https://cheatengine.org/
- Download and install Cheat Engine
- Run as Administrator

STEP 2: Start the Game
- Launch Back 4 Blood
- Go to Solo Campaign or Training (OFFLINE ONLY!)
- Load into any level where you can move

STEP 3: Attach Cheat Engine
- Open Cheat Engine
- Click the computer icon (top-left)
- Select "Back4Blood.exe" from the process list
- Click "Open"

STEP 4: Find Jump Velocity
- In the game, try jumping (note your normal jump height)
- In Cheat Engine:
  * Set "Value Type" to "Float"
  * Enter "420" in the Value field (common jump velocity)
  * Click "First Scan"
- Jump in the game again
- In Cheat Engine:
  * Click "Next Scan" 
  * If too many results, try "Changed value" scan
- Repeat until you have 1-10 addresses

STEP 5: Test and Apply Super Jump
- Double-click an address to add it to the bottom panel
- Change the value to test:
  * Normal: ~420
  * 2x Jump: ~840
  * 3x Jump: ~1260
  * 5x Jump: ~2100
  * 10x Jump: ~4200
- Test in-game - you should jump higher!

STEP 6: Fine-tune
- If jump is too high/low, adjust the value
- You can save the address for future use
- Values reset when you restart the game

⚠️ IMPORTANT SAFETY NOTES:
- ONLY use in offline single-player mode
- Never use in online multiplayer
- Start with small multipliers (2x-3x) first
- Values are temporary and reset on game restart

🎯 TROUBLESHOOTING:
- Can't find process? Run Cheat Engine as Administrator
- Too many search results? Try jumping while scanning
- Game crashes? Lower the jump multiplier value
- Values don't work? Try different addresses from the scan

Happy jumping! 🚀
"""
        
        with open('SUPER_JUMP_MANUAL.txt', 'w') as f:
            f.write(instructions)
        
        print("✅ Created manual instructions: SUPER_JUMP_MANUAL.txt")
        return True
    
    def run(self):
        """Main execution"""
        print("🎮 Back 4 Blood Super Jump Setup")
        print("=" * 40)
        
        # Check if game is running
        if self.find_game():
            print("✅ Game is running - ready for super jump setup!")
        else:
            print("⚠️  Game not found. Please start Back 4 Blood first.")
            print("   Go to Solo Campaign or Training mode (offline only)")
        
        print("\n📝 Creating setup files...")
        
        # Create helper files
        self.create_cheat_engine_script()
        self.create_manual_instructions()
        
        print("\n🎯 QUICK START:")
        print("1. Download Cheat Engine from https://cheatengine.org/")
        print("2. Run Cheat Engine as Administrator") 
        print("3. Open SUPER_JUMP_MANUAL.txt for detailed instructions")
        print("4. Or load back4blood_superjump.lua in Cheat Engine for automation")
        
        print("\n✅ Setup complete! Check the created files for instructions.")
        
        # Keep checking for game
        if not self.game_pid:
            print("\n⏳ Waiting for Back 4 Blood to start...")
            while True:
                if self.find_game():
                    print("✅ Game detected! You can now proceed with the super jump setup.")
                    break
                time.sleep(3)

if __name__ == "__main__":
    helper = CheatEngineHelper()
    helper.run()
    input("\nPress Enter to exit...")
