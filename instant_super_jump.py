#!/usr/bin/env python3
"""
Instant Super Jump for Back 4 Blood
This script will automatically find and modify jump values
"""

import ctypes
import ctypes.wintypes
import struct
import time
import psutil
import sys
import threading

# Windows API setup
kernel32 = ctypes.windll.kernel32
PROCESS_ALL_ACCESS = 0x1F0FFF

class InstantSuperJump:
    def __init__(self):
        self.process_handle = None
        self.base_address = None
        self.jump_addresses = []
        self.original_values = {}
        self.active = False
        
    def find_game_process(self):
        """Find Back 4 Blood process"""
        for proc in psutil.process_iter(['pid', 'name']):
            if 'back4blood' in proc.info['name'].lower():
                return proc.info['pid']
        return None
    
    def open_process(self, pid):
        """Open process for memory access"""
        self.process_handle = kernel32.OpenProcess(PROCESS_ALL_ACCESS, False, pid)
        return self.process_handle is not None
    
    def read_float(self, address):
        """Read float from memory"""
        buffer = ctypes.c_float()
        bytes_read = ctypes.c_size_t()
        
        success = kernel32.ReadProcessMemory(
            self.process_handle,
            ctypes.c_void_p(address),
            ctypes.byref(buffer),
            4,
            ctypes.byref(bytes_read)
        )
        
        return buffer.value if success else None
    
    def write_float(self, address, value):
        """Write float to memory"""
        buffer = ctypes.c_float(value)
        bytes_written = ctypes.c_size_t()
        
        success = kernel32.WriteProcessMemory(
            self.process_handle,
            ctypes.c_void_p(address),
            ctypes.byref(buffer),
            4,
            ctypes.byref(bytes_written)
        )
        
        return success
    
    def scan_memory_region(self, start_addr, size, target_value):
        """Scan a memory region for target value"""
        addresses = []
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.c_size_t()
        
        success = kernel32.ReadProcessMemory(
            self.process_handle,
            ctypes.c_void_p(start_addr),
            buffer,
            size,
            ctypes.byref(bytes_read)
        )
        
        if success:
            target_bytes = struct.pack('<f', target_value)
            data = buffer.raw[:bytes_read.value]
            
            offset = 0
            while True:
                pos = data.find(target_bytes, offset)
                if pos == -1:
                    break
                addresses.append(start_addr + pos)
                offset = pos + 1
        
        return addresses
    
    def find_jump_values(self):
        """Find potential jump velocity values in memory"""
        print("🔍 Scanning for jump velocity values...")
        
        # Common jump velocity values in Unreal Engine games
        target_values = [420.0, 600.0, 500.0, 450.0, 550.0]
        
        # Get memory regions (simplified approach)
        pid = self.find_game_process()
        if not pid:
            return False
            
        process = psutil.Process(pid)
        
        # Scan common memory ranges where game data is typically stored
        scan_ranges = [
            (0x10000000, 0x1000000),  # 16MB scan starting at 256MB
            (0x20000000, 0x1000000),  # 16MB scan starting at 512MB
            (0x30000000, 0x1000000),  # 16MB scan starting at 768MB
        ]
        
        all_addresses = []
        
        for target in target_values:
            print(f"  Searching for {target}...")
            for start, size in scan_ranges:
                try:
                    addresses = self.scan_memory_region(start, size, target)
                    all_addresses.extend(addresses)
                    if addresses:
                        print(f"    Found {len(addresses)} matches at range {hex(start)}")
                except:
                    continue
        
        # Remove duplicates and store
        self.jump_addresses = list(set(all_addresses))
        print(f"✅ Found {len(self.jump_addresses)} potential jump addresses")
        
        return len(self.jump_addresses) > 0
    
    def apply_super_jump(self, multiplier=3.0):
        """Apply super jump to all found addresses"""
        if not self.jump_addresses:
            print("❌ No jump addresses found!")
            return False
        
        success_count = 0
        
        for addr in self.jump_addresses:
            try:
                # Read original value
                original = self.read_float(addr)
                if original and 300 <= original <= 800:  # Reasonable jump velocity range
                    # Store original
                    self.original_values[addr] = original
                    
                    # Apply multiplier
                    new_value = original * multiplier
                    if self.write_float(addr, new_value):
                        success_count += 1
                        print(f"✅ Modified {hex(addr)}: {original:.1f} → {new_value:.1f}")
            except:
                continue
        
        if success_count > 0:
            self.active = True
            print(f"🚀 Super jump activated! Modified {success_count} addresses")
            return True
        else:
            print("❌ Failed to apply super jump")
            return False
    
    def restore_original(self):
        """Restore original jump values"""
        if not self.original_values:
            return
        
        for addr, original in self.original_values.items():
            try:
                self.write_float(addr, original)
                print(f"✅ Restored {hex(addr)} to {original:.1f}")
            except:
                continue
        
        self.active = False
        self.original_values.clear()
        print("🔄 Original jump values restored")
    
    def run(self):
        """Main execution"""
        print("🎮 Back 4 Blood Instant Super Jump")
        print("=" * 40)
        
        # Wait for game to start
        print("⏳ Waiting for Back 4 Blood to start...")
        while True:
            pid = self.find_game_process()
            if pid:
                print(f"✅ Found game process (PID: {pid})")
                break
            time.sleep(2)
        
        # Open process
        if not self.open_process(pid):
            print("❌ Failed to open game process! Try running as Administrator.")
            return
        
        print("✅ Connected to game process")
        
        # Wait a bit for game to fully load
        print("⏳ Waiting for game to load...")
        time.sleep(10)
        
        # Find jump values
        if not self.find_jump_values():
            print("❌ Could not find jump velocity values!")
            print("💡 Try jumping in-game and run this script again")
            return
        
        # Apply super jump
        print("\n🚀 Applying super jump modification...")
        if self.apply_super_jump(3.0):
            print("\n✅ SUPER JUMP ACTIVATED!")
            print("🎯 Go test it in-game - you should jump 3x higher!")
            print("\nControls:")
            print("  Press 1 for 3x jump")
            print("  Press 2 for 5x jump") 
            print("  Press 3 for 10x jump")
            print("  Press 0 to restore normal jump")
            print("  Press Q to quit")
            
            # Interactive control loop
            try:
                while True:
                    choice = input("\nCommand: ").strip().lower()
                    
                    if choice == 'q':
                        break
                    elif choice == '1':
                        self.restore_original()
                        self.apply_super_jump(3.0)
                    elif choice == '2':
                        self.restore_original()
                        self.apply_super_jump(5.0)
                    elif choice == '3':
                        self.restore_original()
                        self.apply_super_jump(10.0)
                    elif choice == '0':
                        self.restore_original()
                    else:
                        print("Invalid command!")
            
            except KeyboardInterrupt:
                pass
            
            # Cleanup
            self.restore_original()
        
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    try:
        mod = InstantSuperJump()
        mod.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")
